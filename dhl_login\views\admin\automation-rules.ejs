<%- include('../layouts/main', { title: title, user: user }) %>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Automation Rules Management</h2>
                <a href="/admin/automation-rules/new" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Rule
                </a>
            </div>

            <!-- Flash Messages -->
            <% if (successMessage && successMessage.length > 0) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <%= successMessage %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% } %>
            <% if (errorMessage && errorMessage.length > 0) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <%= errorMessage %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% } %>

            <!-- Navigation -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
                    <li class="breadcrumb-item active">Automation Rules</li>
                </ol>
            </nav>

            <!-- Rules Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Active Automation Rules</h5>
                </div>
                <div class="card-body">
                    <% if (rules && rules.length > 0) { %>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Source Pattern</th>
                                        <th>Trigger Event</th>
                                        <th>Next Checklist</th>
                                        <th>Assignment Logic</th>
                                        <th>Delay (min)</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% rules.forEach(rule => { %>
                                        <tr>
                                            <td><%= rule.rule_id %></td>
                                            <td>
                                                <code><%= rule.source_checklist_filename_pattern %></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <%= rule.trigger_event.replace('ON_', '').replace('_', ' ') %>
                                                </span>
                                            </td>
                                            <td>
                                                <code><%= rule.next_checklist_filename %></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <%= rule.assignment_logic_type.replace('_', ' ') %>
                                                </span>
                                                <% if (rule.assignment_logic_detail) { %>
                                                    <br><small class="text-muted"><%= rule.assignment_logic_detail %></small>
                                                <% } %>
                                            </td>
                                            <td><%= rule.delay_minutes_after_trigger %></td>
                                            <td>
                                                <% if (rule.is_active) { %>
                                                    <span class="badge bg-success">Active</span>
                                                <% } else { %>
                                                    <span class="badge bg-warning">Inactive</span>
                                                <% } %>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <%= new Date(rule.created_at).toLocaleDateString() %>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/admin/automation-rules/<%= rule.rule_id %>/edit" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger" 
                                                            title="Delete"
                                                            onclick="confirmDelete(<%= rule.rule_id %>, '<%= rule.source_checklist_filename_pattern %>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } else { %>
                        <div class="text-center py-5">
                            <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No automation rules configured</h5>
                            <p class="text-muted">Create your first automation rule to get started with automated checklist assignments.</p>
                            <a href="/admin/automation-rules/new" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Rule
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> How Automation Rules Work
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Trigger Events:</h6>
                            <ul class="list-unstyled">
                                <li><strong>ON_SUBMISSION_COMPLETE:</strong> Triggers when a user submits a checklist</li>
                                <li><strong>ON_SUPERVISOR_VALIDATION:</strong> Triggers when a supervisor validates a checklist</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Assignment Logic Types:</h6>
                            <ul class="list-unstyled">
                                <li><strong>SAME_USER:</strong> Assigns to the same user who triggered the rule</li>
                                <li><strong>SPECIFIC_USER:</strong> Assigns to a specific user ID (enter in detail field)</li>
                                <li><strong>ROLE_BASED_ROUND_ROBIN:</strong> Assigns to users with a specific role (coming soon)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this automation rule?</p>
                <p><strong>Source Pattern:</strong> <span id="deleteRulePattern"></span></p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_csrf" value="<%= typeof _csrf !== 'undefined' ? _csrf : '' %>">
                    <button type="submit" class="btn btn-danger">Delete Rule</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(ruleId, pattern) {
    document.getElementById('deleteRulePattern').textContent = pattern;
    document.getElementById('deleteForm').action = `/admin/automation-rules/${ruleId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<%- include('../layouts/main-footer') %>
