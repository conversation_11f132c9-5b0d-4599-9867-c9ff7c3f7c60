<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checklist #1-A Cell West Side Daily</title>
    <link rel="stylesheet" href="dhl-unified.css">
    <script src="config.js"></script>
    <script defer src="scripts.js"></script>
</head>
<body>
    <input type="text" id="scannerInput" style="position: absolute; left: -9999px; top: -9999px;">
    <div class="App">
        <header class="header">
            <img src="dhl-logo.svg" alt="DHL Logo" class="logo">
        </header>

            <h1>Warehouse Sanitation Checklist</h1>

        <div class="header-inputs">
            <label for="name">Associate Name:</label>
            <input type="text" id="name" name="name" placeholder="Enter your name" required>
            <label for="date">Date:</label>
            <input type="date" id="date" name="date" required>
        </div>
            <section>
                <h2 class="no-wrap-heading">Checklist #1-A Cell West Side Daily</h2>
            </section>
                <h3>Scan barcode to check boxes as sanitation tasks are completed. If unable to complete, leave blank and note reason in comments.</h3>
        
        <div class="task-container">
            
            <section>          
                <div class="task1-inputs">       
                <h3>Wipe Around Door, Dock Lock Boxes, Trac Guards, and Frames Daily A West</h3>
                    <div class="section" id="section1">
                    <div class="grid">
                        <div><input type="checkbox" id="A76" name="A76"><label for="A76"> A76</label></div>
                        <div><input type="checkbox" id="door274" name="door274"><label for="door274"> Door 274</label></div>
                        <div><input type="checkbox" id="door268" name="door268"><label for="door268"> Door 268</label></div>
                        <div><input type="checkbox" id="door279" name="door279"><label for="door279"> Door 279</label></div>
                        <div><input type="checkbox" id="door273" name="door273"><label for="door273"> Door 273</label></div>
                        <div><input type="checkbox" id="door267" name="door267"><label for="door267"> Door 267</label></div>
                        <div><input type="checkbox" id="door278" name="door278"><label for="door278"> Door 278</label></div>
                        <div><input type="checkbox" id="door272" name="door272"><label for="door272"> Door 272</label></div>
                        <div><input type="checkbox" id="door266" name="door266"><label for="door266"> Door 266</label></div>
                        <div><input type="checkbox" id="door277" name="door277"><label for="door277"> Door 277</label></div>
                        <div><input type="checkbox" id="door271" name="door271"><label for="door271"> Door 271</label></div>
                        <div><input type="checkbox" id="door265" name="door265"><label for="door265"> Door 265</label></div>
                        <div><input type="checkbox" id="door276" name="door276"><label for="door276"> Door 276</label></div>
                        <div><input type="checkbox" id="door270" name="door270"><label for="door270"> Door 270</label></div>
                        <div><input type="checkbox" id="A_B_West_Transition" name="A_B_West_Transition"><label for="A_B_West_Transition"> A-B West Transition</label></div>
                        <div><input type="checkbox" id="A75" name="A75"><label for="A75"> A75</label></div>
                        <div><input type="checkbox" id="door269" name="door269"><label for="door269"> Door 269</label></div>
                        <div><input type="checkbox" id="A_B_Mid_Transition" name="A_B_Mid_Transition"><label for="A_B_Mid_Transition"> A-B Mid Transition</label></div>
                        <div><input type="checkbox" id="door275" name="door275"><label for="door275"> Door 275</label></div>
                        <div><input type="checkbox" id="A74" name="A74"><label for="A74"> A74</label></div>
                        <div></div>
                        <div></div>
                    </div>
                </div>            
            </div>
            </section>          
            
            <section>    
            <div class="task2-inputs">
                <h3>Wipe Down Guardrails Daily A West</h3>
                <div class="grid">
                    <div><input type="checkbox" id="South_Walkway_A_West" name="South_Walkway_A_West"><label for="South_Walkway_A_West"> South Walkway</label></div>
                    <div><input type="checkbox" id="East_Aisle_Ends_A_West" name="East_Aisle_Ends_A_West"><label for="East_Aisle_Ends_A_West"> East Aisle Ends</label></div>
                    <div><input type="checkbox" id="West_Aisle_Ends_A_West" name="West_Aisle_Ends_A_West"><label for="West_Aisle_Ends_A_West"> West Aisle Ends</label></div>
                </div>
            </div>
            </section>

            <section>
            <div class="task3-inputs">    
                <h3>Sweep Sanitation Lines Daily A West</h3>            
                <div class="grid">
                    <div><input type="checkbox" id="West_Wall_A_West" name="West_Wall_A_West"><label for="West_Wall_A_West"> West Wall</label></div>
                    <div><input type="checkbox" id="North_Wall_A_West" name="North_Wall_A_West"><label for="North_Wall_A_West"> North Wall</label></div>
                
                </div>
            </div>
            </section>

            <section>
            <div class="task4-inputs">    
                <h3>Pick Up Debris Daily A West</h3>            
                <div class="grid">
                    <div><input type="checkbox" id="West_Dock_Awest" name="West_Dock_Awest"><label for="West_Dock_Awest"> West Dock</label></div>
                    <div><input type="checkbox" id="Walkway_Awest" name="Walkway_Awest"><label for="Walkway_Awest"> West Walkway</label></div>
                
                </div>
            </div>
            </section>

            <div class="comments">
                <label for="comments">Comments:</label>
                <textarea id="comments" name="comments" rows="5" cols="133" placeholder="Enter comments here..."></textarea>
            </div>



            <div class="button">
                <input type="submit" value="Submit">
            </div>
        </div>

        <div class="footer" style="text-align: center; margin-top: 2rem; font-size: 0.8rem; color: #666;">
            &copy; 2025 DHL Supply Chain | Warehouse Sanitation Checklists
        </div>
    </div>
</body>
</html>

