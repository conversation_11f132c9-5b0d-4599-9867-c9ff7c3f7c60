<style>
  .postgresql-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .dashboard-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .dashboard-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #2e7d32;
    transition: transform 0.2s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2e7d32;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .recent-submissions {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }

  .recent-submissions h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #2e7d32;
    padding-bottom: 0.5rem;
  }

  .submissions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
  }

  .submissions-table th,
  .submissions-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  .submissions-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
  }

  .submissions-table tr:hover {
    background-color: #f9f9f9;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-pendingsupervisorvalidation {
    background-color: #d1ecf1;
    color: #0c5460;
  }

  .status-supervisorvalidated {
    background-color: #d4edda;
    color: #155724;
  }

  .status-completed {
    background-color: #d4edda;
    color: #155724;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .btn-primary {
    background-color: #2e7d32;
    color: white;
  }

  .btn-primary:hover {
    background-color: #1b5e20;
    color: white;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
    color: white;
  }

  .no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
  }

  @media (max-width: 768px) {
    .postgresql-dashboard {
      padding: 1rem;
    }

    .dashboard-header {
      padding: 1.5rem;
    }

    .dashboard-header h1 {
      font-size: 2rem;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .submissions-table {
      font-size: 0.9rem;
    }

    .action-buttons {
      flex-direction: column;
    }
  }
</style>

<div class="postgresql-dashboard">
  <div class="dashboard-header">
    <h1>📊 PostgreSQL Data Dashboard</h1>
    <p>Monitor checklist submissions, validations, and system activity</p>
  </div>

  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number"><%= stats.submissions %></div>
      <div class="stat-label">Total Submissions</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><%= stats.validations %></div>
      <div class="stat-label">Supervisor Validations</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><%= stats.tasks %></div>
      <div class="stat-label">Total Tasks</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><%= stats.audit %></div>
      <div class="stat-label">Audit Trail Entries</div>
    </div>
  </div>

  <div class="recent-submissions">
    <h2>Recent Submissions</h2>
    <% if (recentSubmissions && recentSubmissions.length > 0) { %>
      <table class="submissions-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Title</th>
            <th>Submitted By</th>
            <th>Date</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <% recentSubmissions.forEach(submission => { %>
            <tr>
              <td><a href="/admin/postgresql/submissions/<%= submission.submission_id %>" style="color: #2e7d32; text-decoration: none;"><%= submission.submission_id %></a></td>
              <td><%= submission.checklist_title %></td>
              <td><%= submission.submitted_by_username %></td>
              <td><%= new Date(submission.submission_timestamp).toLocaleDateString() %></td>
              <td>
                <span class="status-badge status-<%= submission.status.toLowerCase().replace(/\s+/g, '') %>">
                  <%= submission.status %>
                </span>
              </td>
            </tr>
          <% }); %>
        </tbody>
      </table>
    <% } else { %>
      <div class="no-data">No submissions found in the database.</div>
    <% } %>
  </div>

  <div class="action-buttons">
    <a href="/admin/postgresql/submissions" class="btn btn-primary">📋 View All Submissions</a>
    <a href="/admin" class="btn btn-secondary">← Back to Admin Dashboard</a>
  </div>
</div>
